@startuml DevSecOps Pipeline Architecture Overview
!theme aws-orange
title DevSecOps Pipeline Architecture Overview for Banking Software Development

package "Development Phase" {
    [Developer Workstation] as DEV
    [IDE with Security Plugins] as IDE
    [Pre-commit Hooks] as PRE
    DEV --> IDE
    IDE --> PRE
}

package "Source Control" {
    [Git Repository] as GIT
    [Branch Protection] as <PERSON><PERSON><PERSON>
    [Secrets Scanning] as SECRETS
    PRE --> GIT
    GIT --> BRANCH
    GIT --> SECRETS
}

package "CI/CD Pipeline" {
    [Build Stage] as BUILD
    [Static Analysis] as SAST
    [Unit Tests] as UNIT
    [Dependency Scan] as SCA
    
    BUILD --> SAST
    BUILD --> UNIT
    BUILD --> SCA
}

package "Security Testing" {
    [Dynamic Analysis] as DAST
    [Interactive Testing] as IAST
    [Penetration Testing] as PENTEST
    [Compliance Scan] as COMPLIANCE
    
    SAST --> DAST
    DAST --> IAST
    IAST --> PENTEST
    PENTEST --> COMPLIANCE
}

package "Deployment" {
    [Staging Environment] as STAGING
    [Production Environment] as PROD
    [Canary Deployment] as <PERSON><PERSON><PERSON><PERSON>
    [Automated Rollback] as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    
    COMPLIANCE --> STAGING
    STAGING --> <PERSON><PERSON><PERSON><PERSON>
    CANARY --> PROD
    PROD --> ROLLBACK
}

package "Monitoring & Response" {
    [SIEM/SOC] as SIEM
    [Fraud Detection] as FRAUD
    [Audit Logging] as AUDIT
    [Incident Response] as INCIDENT
    
    PROD --> SIEM
    SIEM --> FRAUD
    SIEM --> AUDIT
    SIEM --> INCIDENT
}

GIT --> BUILD

note right of DEV : Security plugins integrated\ninto development environment
note right of SAST : SonarQube, Checkmarx\nVulnerability scanning
note right of PROD : Blue-green deployment\nZero-downtime releases
note right of SIEM : 24/7 monitoring\nReal-time threat detection

@enduml
