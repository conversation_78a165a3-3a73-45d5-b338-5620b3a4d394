@startuml Monitoring and Observability Architecture
!theme aws-orange
title Monitoring and Observability Architecture for Banking Systems

package "Application Layer" #LightBlue {
    [Core Banking App] as CORE_APP
    [Payment Service] as PAYMENT_SVC
    [Authentication Service] as AUTH_SVC
    [Fraud Detection] as FRAUD_SVC
    [API Gateway] as API_GW
}

package "Infrastructure Layer" #LightGreen {
    [Kubernetes Cluster] as K8S
    [Database Servers] as DB_SERVERS
    [Load Balancers] as LB
    [Network Devices] as NETWORK
    [Storage Systems] as STORAGE
}

package "Data Collection" #LightYellow {
    [Application Metrics] as APP_METRICS
    [Infrastructure Metrics] as INFRA_METRICS
    [Application Logs] as APP_LOGS
    [Security Logs] as SEC_LOGS
    [Audit Logs] as AUDIT_LOGS
    [Distributed Traces] as TRACES
}

package "Data Processing" #LightCoral {
    [Prometheus] as PROMETHEUS
    [Elasticsearch] as ELASTICSEARCH
    [Logstash] as LOGS<PERSON><PERSON><PERSON>
    [J<PERSON><PERSON>] as <PERSON><PERSON><PERSON><PERSON>
    [Fluentd] as FLUENTD
}

package "Storage" #LightCyan {
    [Time Series DB] as TSDB
    [Log Storage] as LOG_STORAGE
    [Trace Storage] as TRACE_STORAGE
    [Long-term Archive] as ARCHIVE
}

package "Visualization & Alerting" #Lavender {
    [Grafana Dashboards] as GRAFANA
    [Kibana] as KIBANA
    [AlertManager] as ALERT_MGR
    [PagerDuty] as PAGERDUTY
    [Slack Notifications] as SLACK
}

package "Security Monitoring" #LightGray {
    [SIEM Platform] as SIEM
    [Threat Intelligence] as THREAT_INTEL
    [Anomaly Detection] as ANOMALY
    [Compliance Monitoring] as COMPLIANCE_MON
}

package "Business Monitoring" #Pink {
    [Transaction Monitoring] as TXN_MON
    [SLA Monitoring] as SLA_MON
    [Customer Experience] as CX_MON
    [Revenue Tracking] as REVENUE_MON
}

' Application to Data Collection
CORE_APP --> APP_METRICS
CORE_APP --> APP_LOGS
CORE_APP --> TRACES
PAYMENT_SVC --> APP_METRICS
PAYMENT_SVC --> APP_LOGS
AUTH_SVC --> SEC_LOGS
FRAUD_SVC --> AUDIT_LOGS
API_GW --> APP_METRICS

' Infrastructure to Data Collection
K8S --> INFRA_METRICS
DB_SERVERS --> INFRA_METRICS
LB --> INFRA_METRICS
NETWORK --> SEC_LOGS
STORAGE --> INFRA_METRICS

' Data Collection to Processing
APP_METRICS --> PROMETHEUS
INFRA_METRICS --> PROMETHEUS
APP_LOGS --> LOGSTASH
SEC_LOGS --> LOGSTASH
AUDIT_LOGS --> FLUENTD
TRACES --> JAEGER

' Processing to Storage
PROMETHEUS --> TSDB
LOGSTASH --> ELASTICSEARCH
FLUENTD --> ELASTICSEARCH
JAEGER --> TRACE_STORAGE
ELASTICSEARCH --> LOG_STORAGE

' Storage to Visualization
TSDB --> GRAFANA
LOG_STORAGE --> KIBANA
TRACE_STORAGE --> JAEGER
PROMETHEUS --> ALERT_MGR

' Alerting
ALERT_MGR --> PAGERDUTY
ALERT_MGR --> SLACK
GRAFANA --> ALERT_MGR

' Security Monitoring
SEC_LOGS --> SIEM
AUDIT_LOGS --> SIEM
SIEM --> THREAT_INTEL
SIEM --> ANOMALY
SIEM --> COMPLIANCE_MON

' Business Monitoring
CORE_APP --> TXN_MON
PAYMENT_SVC --> TXN_MON
API_GW --> SLA_MON
CORE_APP --> CX_MON
PAYMENT_SVC --> REVENUE_MON

' Long-term Storage
LOG_STORAGE --> ARCHIVE
TSDB --> ARCHIVE

note right of PROMETHEUS : Metrics collection\n15s scrape interval\n30-day retention
note right of ELASTICSEARCH : Log aggregation\nFull-text search\n90-day retention
note right of SIEM : Real-time analysis\nThreat correlation\nCompliance reporting
note right of GRAFANA : Executive dashboards\nOperational views\nCustom alerts
note right of TXN_MON : Real-time transaction\nmonitoring and fraud\ndetection metrics

@enduml
