# Architecture Diagrams

This directory contains PlantUML diagrams that illustrate the various aspects of the DevSecOps pipeline for banking software development.

## Diagram Index

### 1. Pipeline Overview
**File:** `pipeline-overview.puml`
**Description:** Complete DevSecOps pipeline architecture showing the flow from development to monitoring, including all security gates and integration points.

### 2. Security Layers
**File:** `security-layers.puml`
**Description:** Multi-layered security architecture implementing defense-in-depth strategy for banking applications, from external protection to core services.

### 3. CI/CD Security Gates
**File:** `cicd-security-gates.puml`
**Description:** Detailed CI/CD pipeline with seven security gates, showing decision points, security tools integration, and failure handling.

### 4. Compliance Framework
**File:** `compliance-framework.puml`
**Description:** Banking compliance framework mapping regulatory standards (PCI DSS, SOX, Basel III, GDPR) to controls and monitoring.

### 5. Infrastructure as Code Security
**File:** `iac-security.puml`
**Description:** IaC security pipeline showing Terraform, Ansible, and Helm security scanning, compliance validation, and deployment automation.

### 6. Incident Response
**File:** `incident-response.puml`
**Description:** Complete incident response and recovery workflow with severity classification, notification procedures, and remediation steps.

### 7. Data Flow Security
**File:** `data-flow-security.puml`
**Description:** Secure data flow architecture showing how customer data moves through different security zones with encryption and access controls.

### 8. Threat Model
**File:** `threat-model.puml`
**Description:** STRIDE-based threat model for banking applications, mapping threat actors to attack vectors, assets, and security controls.

### 9. Deployment Architecture
**File:** `deployment-architecture.puml`
**Description:** Multi-environment deployment architecture (Dev, Test, Staging, Production, DR) with CI/CD integration and security controls.

### 10. Monitoring and Observability
**File:** `monitoring-observability.puml`
**Description:** Comprehensive monitoring architecture covering application metrics, infrastructure monitoring, security monitoring, and business metrics.

## How to Use These Diagrams

### Viewing PlantUML Diagrams

1. **Online PlantUML Editor:**
   - Visit: http://www.plantuml.com/plantuml/uml/
   - Copy and paste the diagram content
   - View the rendered diagram

2. **VS Code Extension:**
   - Install "PlantUML" extension
   - Open `.puml` files in VS Code
   - Use `Alt+D` to preview

3. **Local PlantUML Installation:**
   ```bash
   # Install PlantUML
   npm install -g node-plantuml
   
   # Generate PNG from PUML
   puml generate pipeline-overview.puml
   ```

4. **Docker:**
   ```bash
   # Run PlantUML server
   docker run -d -p 8080:8080 plantuml/plantuml-server:jetty
   
   # Access at http://localhost:8080
   ```

### Diagram Categories

#### **Security Architecture**
- `security-layers.puml` - Defense-in-depth layers
- `data-flow-security.puml` - Secure data flow
- `threat-model.puml` - STRIDE threat analysis

#### **Pipeline Architecture**
- `pipeline-overview.puml` - Complete DevSecOps pipeline
- `cicd-security-gates.puml` - Security gates detail
- `iac-security.puml` - Infrastructure security

#### **Compliance & Governance**
- `compliance-framework.puml` - Regulatory compliance
- `incident-response.puml` - Incident handling

#### **Operations & Monitoring**
- `deployment-architecture.puml` - Environment setup
- `monitoring-observability.puml` - Monitoring stack

## Diagram Standards

### Color Coding
- **Light Blue (#LightBlue):** External/User interfaces
- **Light Green (#LightGreen):** Secure/Approved processes
- **Light Yellow (#LightYellow):** Processing/Validation stages
- **Light Coral (#LightCoral):** Critical/High-risk components
- **Light Cyan (#LightCyan):** Monitoring/Observability
- **Lavender (#Lavender):** Tools/Utilities
- **Light Gray (#LightGray):** Infrastructure components

### Notation Standards
- **Rectangles:** Services/Components
- **Actors:** External entities (users, systems)
- **Packages:** Logical groupings
- **Arrows:** Data/Control flow
- **Notes:** Additional context/details

### Security Indicators
- 🔴 Critical security controls
- 🟠 High priority security measures
- 🟡 Medium priority controls
- 🟢 Standard security practices
- 🚨 Alert/Incident indicators
- ❌ Failure/Block indicators

## Maintenance

### Updating Diagrams
1. Modify the `.puml` source files
2. Test rendering with PlantUML
3. Update this README if new diagrams are added
4. Ensure consistency with architecture documentation

### Version Control
- All diagrams are version controlled
- Use meaningful commit messages
- Tag major architectural changes
- Maintain backward compatibility when possible

### Review Process
- Architecture changes require review
- Security team approval for security diagrams
- Compliance team review for regulatory diagrams
- Documentation team review for clarity

## Related Documentation

- [Architecture Overview](../README.md)
- [Security Documentation](../../security/README.md)
- [Compliance Documentation](../../compliance/README.md)
- [Operations Documentation](../../operations/README.md)

## Tools and Resources

### PlantUML Resources
- [PlantUML Official Site](https://plantuml.com/)
- [PlantUML Language Reference](https://plantuml.com/guide)
- [PlantUML Themes](https://plantuml.com/theme)
- [Real World PlantUML](https://real-world-plantuml.com/)

### Banking Security Standards
- [PCI DSS Requirements](https://www.pcisecuritystandards.org/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [ISO 27001 Standard](https://www.iso.org/isoiec-27001-information-security.html)
- [OWASP Banking Security](https://owasp.org/www-project-application-security-verification-standard/)

---

**Note:** These diagrams represent the current state of the DevSecOps architecture. They should be updated as the system evolves and new security requirements are identified.
