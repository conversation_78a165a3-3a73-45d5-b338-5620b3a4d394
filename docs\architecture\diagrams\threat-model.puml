@startuml Banking Application Threat Model
!theme aws-orange
title Banking Application Threat Model - STRIDE Analysis

package "External Threats" #LightCoral {
    actor "Cybercriminals" as CRIMINALS
    actor "Nation State Actors" as NATION_STATE
    actor "Hacktivists" as HACKTIVISTS
    actor "Insider Threats" as INSIDER
}

package "Attack Vectors" #LightYellow {
    [Web Application Attacks] as WEB_ATTACKS
    [API Attacks] as API_ATTACKS
    [Social Engineering] as SOCIAL_ENG
    [Phishing] as PHISHING
    [Malware] as MALWARE
    [DDoS] as DDOS
}

package "Banking Assets" #LightGreen {
    [Customer Data] as CUSTOMER_DATA
    [Financial Transactions] as TRANSACTIONS
    [Account Information] as ACCOUNTS
    [Payment Systems] as PAYMENTS
    [Core Banking System] as CORE_BANKING
    [Regulatory Data] as REGULATORY_DATA
}

package "STRIDE Threats" #LightBlue {
    [Spoofing Identity] as SPOOFING
    [Tampering with Data] as TAMPERING
    [Repudiation] as REPUDIATION
    [Information Disclosure] as INFO_DISCLOSURE
    [Denial of Service] as DOS
    [Elevation of Privilege] as ELEVATION
}

package "Security Controls" #LightCyan {
    [Multi-Factor Authentication] as MFA
    [Encryption] as ENCRYPTION
    [Digital Signatures] as SIGNATURES
    [Access Controls] as ACCESS_CTRL
    [Audit Logging] as AUDIT_LOG
    [Network Segmentation] as NETWORK_SEG
    [Intrusion Detection] as IDS
    [Data Loss Prevention] as DLP
}

package "Compliance Requirements" #Lavender {
    [PCI DSS] as PCI
    [SOX] as SOX
    [GDPR] as GDPR
    [Basel III] as BASEL
    [AML/KYC] as AML
}

' Threat Actor to Attack Vector relationships
CRIMINALS --> WEB_ATTACKS
CRIMINALS --> API_ATTACKS
NATION_STATE --> MALWARE
NATION_STATE --> DDOS
HACKTIVISTS --> DDOS
HACKTIVISTS --> WEB_ATTACKS
INSIDER --> SOCIAL_ENG
INSIDER --> ELEVATION

' Attack Vector to Asset relationships
WEB_ATTACKS --> CUSTOMER_DATA
API_ATTACKS --> TRANSACTIONS
SOCIAL_ENG --> ACCOUNTS
PHISHING --> CUSTOMER_DATA
MALWARE --> CORE_BANKING
DDOS --> PAYMENTS

' Asset to STRIDE relationships
CUSTOMER_DATA --> SPOOFING
TRANSACTIONS --> TAMPERING
ACCOUNTS --> REPUDIATION
PAYMENTS --> INFO_DISCLOSURE
CORE_BANKING --> DOS
REGULATORY_DATA --> ELEVATION

' STRIDE to Security Controls relationships
SPOOFING --> MFA
TAMPERING --> ENCRYPTION
REPUDIATION --> SIGNATURES
INFO_DISCLOSURE --> ACCESS_CTRL
DOS --> NETWORK_SEG
ELEVATION --> AUDIT_LOG

' Additional security controls
CUSTOMER_DATA --> DLP
TRANSACTIONS --> IDS
CORE_BANKING --> NETWORK_SEG

' Compliance mapping
CUSTOMER_DATA --> PCI
TRANSACTIONS --> SOX
ACCOUNTS --> GDPR
PAYMENTS --> BASEL
REGULATORY_DATA --> AML

note right of SPOOFING : Identity verification\nAuthentication bypass\nSession hijacking
note right of TAMPERING : Data modification\nTransaction manipulation\nCode injection
note right of REPUDIATION : Non-repudiation\nAudit trail integrity\nDigital evidence
note right of INFO_DISCLOSURE : Data breaches\nUnauthorized access\nPrivacy violations
note right of DOS : Service disruption\nResource exhaustion\nBusiness continuity
note right of ELEVATION : Privilege escalation\nUnauthorized access\nSystem compromise

@enduml
