@startuml Infrastructure as Code Security
!theme aws-orange
title Infrastructure as Code (IaC) Security Pipeline

actor <PERSON><PERSON><PERSON> as DEV

package "Development" #LightBlue {
    [Terraform Code] as TERRAFORM
    [Ansible Playbooks] as ANSIBLE
    [Helm Charts] as HELM
    DEV --> TERRAFORM
    DEV --> ANSIBLE
    DEV --> HELM
}

package "Version Control" #LightGreen {
    [Git Repository] as GIT
    [Branch Protection] as BRANCH
    [Code Review] as REVIEW
    TERRAFORM --> GIT
    ANSIBLE --> GIT
    HELM --> GIT
    GIT --> BRANCH
    BRANCH --> REVIEW
}

package "IaC Security Scanning" #LightY<PERSON>w {
    [Checkov Scanner] as <PERSON>ECKOV
    [TFS<PERSON>] as TFSEC
    [Kube-score] as <PERSON><PERSON><PERSON>
    [O<PERSON> Policies] as POLICY
    REVIEW --> CHECKOV
    REVIEW --> TFSEC
    REVIEW --> KUBE
    REVIEW --> POLICY
}

package "Compliance Validation" #LightCoral {
    [CIS Benchmarks] as C<PERSON>
    [SOC 2 Controls] as SOC2
    [PCI DSS Requirements] as PCIDSS
    [GDPR Compliance] as GDPR_CHECK
    CHECKOV --> CIS
    TFSEC --> SOC2
    KUBE --> PCIDSS
    POLICY --> GDPR_CHECK
}

package "Deployment Pipeline" #LightCyan {
    [Terraform Plan] as PLAN
    [Validation] as VALIDATE
    [Terraform Apply] as APPLY
    [Drift Detection] as DRIFT
    CIS --> PLAN
    SOC2 --> PLAN
    PLAN --> VALIDATE
    VALIDATE --> APPLY
    APPLY --> DRIFT
}

package "Infrastructure Monitoring" #Lavender {
    [Config Monitoring] as CONFIG
    [Compliance Monitoring] as COMPLIANCE_MON
    [IaC Alerts] as ALERT_IAC
    [Auto-Remediation] as REMEDIATE
    DRIFT --> CONFIG
    CONFIG --> COMPLIANCE_MON
    COMPLIANCE_MON --> ALERT_IAC
    ALERT_IAC --> REMEDIATE
}

package "Security Controls" #LightGray {
    [RBAC Policies] as RBAC
    [Network Policies] as NETWORK
    [Secret Management] as SECRETS
    [Encryption at Rest] as ENCRYPTION
    APPLY --> RBAC
    APPLY --> NETWORK
    APPLY --> SECRETS
    APPLY --> ENCRYPTION
}

note right of CHECKOV : 800+ built-in policies\nAWS, Azure, GCP support\nCustom policy creation
note right of TFSEC : Terraform security scanner\nStatic analysis\nMisconfigurations detection
note right of POLICY : Open Policy Agent\nPolicy as Code\nCompliance automation
note right of DRIFT : Continuous monitoring\nConfiguration drift detection\nAuto-remediation triggers

@enduml
