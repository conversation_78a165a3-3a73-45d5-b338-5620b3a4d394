@startuml CI/CD Pipeline with Security Gates
!theme aws-orange
title CI/CD Pipeline with Security Gates for Banking Applications

start

:Developer Commits Code;
:Pipeline Triggered;

if (Security Gate 1\nPre-Build Validation) then (Pass)
    :Build Application;
else (Fail)
    :❌ Build Blocked;
    stop
endif

if (Security Gate 2\nStatic Analysis) then (Pass)
    :Unit Tests;
else (Fail)
    :❌ Static Analysis Failed;
    stop
endif

if (Security Gate 3\nDependency Check) then (Pass)
    :Package Application;
else (Fail)
    :❌ Vulnerable Dependencies;
    stop
endif

if (Security Gate 4\nContainer Scan) then (Pass)
    :Deploy to Staging;
else (Fail)
    :❌ Container Vulnerabilities;
    stop
endif

if (Security Gate 5\nDynamic Testing) then (Pass)
    :Compliance Validation;
else (Fail)
    :❌ Runtime Vulnerabilities;
    stop
endif

if (Security Gate 6\nCompliance Check) then (Pass)
    :Manual Approval;
else (Fail)
    :❌ Compliance Violation;
    stop
endif

if (Security Gate 7\nProduction Readiness) then (Pass)
    :Deploy to Production;
else (Fail)
    :❌ Not Production Ready;
    stop
endif

:Continuous Monitoring;

if (Security Incident?) then (Yes)
    :Incident Response;
    :Automated Rollback;
    :Investigation;
    stop
else (No)
    :Continue Monitoring;
    :Continuous Monitoring;
endif

note right of "Security Gate 2" : Tools: SonarQube\nCheckmarx, Veracode
note right of "Security Gate 3" : Tools: Snyk\nWhiteSource, Black Duck
note right of "Security Gate 4" : Tools: Aqua Security\nTwistlock, Clair
note right of "Security Gate 5" : Tools: OWASP ZAP\nBurp Suite, Rapid7
note right of "Security Gate 6" : PCI DSS Scanner\nSOX Validator\nGDPR Checker

@enduml
