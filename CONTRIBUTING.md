# Contributing to DevSecOps Banking Pipeline

Thank you for your interest in contributing to our DevSecOps pipeline for banking software development. This document provides guidelines and procedures for contributing to this project.

## Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md).

## Security First

Given the banking domain, security is our top priority. All contributions must:
- Follow secure coding practices
- Include security testing
- Pass compliance validation
- Undergo security review

## Getting Started

1. **Fork the repository**
2. **Set up your development environment**
3. **Read the documentation** in the `docs/` directory
4. **Understand the compliance requirements**

## Development Process

### 1. Issue Creation
- Check existing issues before creating new ones
- Use appropriate issue templates
- Label issues correctly (security, compliance, enhancement, bug)

### 2. Branch Strategy
- Create feature branches from `main`
- Use descriptive branch names: `feature/security-enhancement`, `fix/compliance-issue`
- Keep branches focused and small

### 3. Commit Guidelines
- Use conventional commit format
- Sign your commits
- Include security impact in commit messages
- Reference related issues

### 4. Pull Request Process
- Fill out the PR template completely
- Include security and compliance checklists
- Ensure all tests pass
- Request appropriate reviewers

## Security Requirements

### Code Security
- No hardcoded secrets or credentials
- Input validation and sanitization
- Proper error handling
- Secure communication protocols

### Testing Requirements
- Unit tests with >90% coverage
- Security tests (SAST/DAST)
- Compliance validation tests
- Integration tests

### Documentation
- Update relevant documentation
- Include security considerations
- Document compliance implications
- Provide examples and usage

## Compliance Checklist

Before submitting any contribution, ensure:
- [ ] PCI DSS requirements considered
- [ ] SOX compliance maintained
- [ ] Data privacy regulations followed
- [ ] Audit trail preserved
- [ ] Risk assessment completed

## Review Process

1. **Automated Checks**: CI/CD pipeline validation
2. **Security Review**: Security team approval required
3. **Compliance Review**: Compliance officer sign-off
4. **Code Review**: Peer review by team members
5. **Final Approval**: Maintainer approval

## Questions?

- Check the [documentation](docs/)
- Search existing [issues](../../issues)
- Contact the maintainers

Thank you for contributing to secure banking software development!
