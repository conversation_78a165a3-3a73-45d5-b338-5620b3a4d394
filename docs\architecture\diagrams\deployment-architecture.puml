@startuml Deployment Architecture
!theme aws-orange
title Banking Application Deployment Architecture - Multi-Environment

package "Development Environment" #LightBlue {
    [Dev Kubernetes Cluster] as DEV_K8S
    [Dev Database] as DEV_DB
    [Dev Redis] as DEV_REDIS
    [Mock Services] as MOCK_SERVICES
    
    DEV_K8S --> DEV_DB
    DEV_K8S --> DEV_REDIS
    DEV_K8S --> MOCK_SERVICES
}

package "Testing Environment" #LightYellow {
    [Test Kubernetes Cluster] as TEST_K8S
    [Test Database] as TEST_DB
    [Test Redis] as TEST_REDIS
    [Security Testing Tools] as SEC_TOOLS
    
    TEST_K8S --> TEST_DB
    TEST_K8S --> TEST_REDIS
    TEST_K8S --> SEC_TOOLS
}

package "Staging Environment" #LightGreen {
    [Staging Kubernetes Cluster] as STAGE_K8S
    [Staging Database] as STAGE_DB
    [Staging Redis] as STAGE_REDIS
    [Performance Testing] as PERF_TOOLS
    
    STAGE_K8S --> STAGE_DB
    STAGE_K8S --> STAGE_REDIS
    STAGE_K8S --> PERF_TOOLS
}

package "Production Environment" #LightCoral {
    [Production Kubernetes Cluster] as PROD_K8S
    [Primary Database] as PROD_DB_PRIMARY
    [Secondary Database] as PROD_DB_SECONDARY
    [Redis Cluster] as PROD_REDIS
    [Monitoring Stack] as MONITORING
    
    PROD_K8S --> PROD_DB_PRIMARY
    PROD_DB_PRIMARY --> PROD_DB_SECONDARY : Replication
    PROD_K8S --> PROD_REDIS
    PROD_K8S --> MONITORING
}

package "Disaster Recovery" #LightCyan {
    [DR Kubernetes Cluster] as DR_K8S
    [DR Database] as DR_DB
    [DR Redis] as DR_REDIS
    [Backup Storage] as BACKUP
    
    DR_K8S --> DR_DB
    DR_K8S --> DR_REDIS
    DR_K8S --> BACKUP
}

package "CI/CD Pipeline" #Lavender {
    [Jenkins/GitLab CI] as CICD
    [Container Registry] as REGISTRY
    [Helm Repository] as HELM_REPO
    [Artifact Repository] as ARTIFACTS
    
    CICD --> REGISTRY
    CICD --> HELM_REPO
    CICD --> ARTIFACTS
}

package "Security & Monitoring" #LightGray {
    [SIEM Platform] as SIEM
    [Vulnerability Scanner] as VULN_SCANNER
    [Secret Management] as VAULT
    [Certificate Management] as CERT_MANAGER
    
    SIEM --> VULN_SCANNER
    SIEM --> VAULT
    SIEM --> CERT_MANAGER
}

' CI/CD to Environments
CICD --> DEV_K8S : Deploy
CICD --> TEST_K8S : Deploy
CICD --> STAGE_K8S : Deploy
CICD --> PROD_K8S : Deploy

' Production to DR
PROD_DB_PRIMARY --> DR_DB : Async Replication
PROD_REDIS --> DR_REDIS : Backup Sync
MONITORING --> BACKUP : Backup Data

' Security Integration
VAULT --> DEV_K8S : Secrets
VAULT --> TEST_K8S : Secrets
VAULT --> STAGE_K8S : Secrets
VAULT --> PROD_K8S : Secrets
VAULT --> DR_K8S : Secrets

SIEM --> DEV_K8S : Monitoring
SIEM --> TEST_K8S : Monitoring
SIEM --> STAGE_K8S : Monitoring
SIEM --> PROD_K8S : Monitoring
SIEM --> DR_K8S : Monitoring

' Certificate Management
CERT_MANAGER --> PROD_K8S : TLS Certificates
CERT_MANAGER --> DR_K8S : TLS Certificates

note right of DEV_K8S : Namespace isolation\nResource limits\nDevelopment tools
note right of TEST_K8S : Automated testing\nSecurity scanning\nCompliance validation
note right of STAGE_K8S : Production-like\nPerformance testing\nUser acceptance testing
note right of PROD_K8S : High availability\nAuto-scaling\n99.99% uptime SLA
note right of DR_K8S : RTO: 4 hours\nRPO: 1 hour\nCross-region deployment

@enduml
