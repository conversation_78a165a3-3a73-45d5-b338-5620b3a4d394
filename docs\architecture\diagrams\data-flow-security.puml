@startuml Data Flow Security Architecture
!theme aws-orange
title Data Flow Security Architecture for Banking Applications

actor "Customer" as CUSTOMER
actor "Bank Employee" as EMPLOYEE
actor "Admin" as ADMIN

package "External Zone" #LightBlue {
    [Web Portal] as WEB
    [Mobile App] as MO<PERSON>LE
    [ATM Network] as ATM
}

package "DMZ (Demilitarized Zone)" #LightYellow {
    [Web Application Firewall] as WAF
    [Load Balancer] as LB
    [Reverse Proxy] as PROXY
}

package "Application Zone" #LightGreen {
    [Authentication Service] as AUTH
    [API Gateway] as API
    [Core Banking API] as CORE_API
    [Payment Service] as PAYMENT_API
}

package "Business Logic Zone" #LightCoral {
    [Account Management] as ACCOUNT
    [Transaction Processing] as TRANSACTION
    [Fraud Detection] as FRAUD
    [Risk Engine] as RISK
}

package "Data Zone" #LightCyan {
    [Customer Database] as CUSTOMER_DB
    [Transaction Database] as TRANSACTION_DB
    [Audit Database] as AUDIT_DB
    [Data Warehouse] as DW
}

package "Security Services" #Lavender {
    [HSM] as HSM
    [Key Management] as KMS
    [SIEM] as SIEM
    [Audit Service] as AUDIT_SVC
}

' External connections
CUSTOMER --> WEB : HTTPS/TLS 1.3
CUSTOMER --> MOBILE : HTTPS/TLS 1.3
CUSTOMER --> ATM : Encrypted

' DMZ processing
WEB --> WAF : Filtered Traffic
MOBILE --> WAF : Filtered Traffic
ATM --> WAF : Filtered Traffic
WAF --> LB : Clean Traffic
LB --> PROXY : Load Balanced

' Application layer
PROXY --> AUTH : Authentication
AUTH --> API : Authorized Requests
API --> CORE_API : Internal API
API --> PAYMENT_API : Payment Requests

' Business logic
CORE_API --> ACCOUNT : Account Operations
CORE_API --> TRANSACTION : Transaction Processing
PAYMENT_API --> FRAUD : Fraud Check
TRANSACTION --> RISK : Risk Assessment

' Data access
ACCOUNT --> CUSTOMER_DB : Encrypted Queries
TRANSACTION --> TRANSACTION_DB : Encrypted Writes
FRAUD --> AUDIT_DB : Audit Logs
RISK --> DW : Analytics Data

' Security integration
AUTH --> HSM : Key Operations
API --> KMS : Encryption Keys
TRANSACTION --> SIEM : Security Events
FRAUD --> AUDIT_SVC : Audit Trail

' Employee access
EMPLOYEE --> AUTH : MFA Authentication
ADMIN --> AUTH : Privileged Access

note right of WAF : DDoS protection\nOWASP Top 10 filtering\nRate limiting
note right of AUTH : Multi-factor authentication\nSSO integration\nSession management
note right of HSM : Hardware security module\nCryptographic operations\nKey generation/storage
note right of FRAUD : Real-time fraud detection\nML-based analysis\nRule engine
note right of AUDIT_SVC : Immutable audit logs\nCompliance reporting\nForensic analysis

@enduml
